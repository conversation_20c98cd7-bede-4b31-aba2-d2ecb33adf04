/**
 * ReplyPal - Background Script
 * Handles extension initialization, context menu, and message passing
 */

// Track tabs with content scripts ready
const readyTabs = new Set();

// Initialize context menu when extension is installed or updated
chrome.runtime.onInstalled.addListener(() => {
  console.log('=== DEBUG: ReplyPal extension installed/updated ===');

  // Create context menu item
  chrome.contextMenus.create({
    id: 'replypal',
    title: 'Generate with ReplyPal',
    contexts: ['selection', 'page']
  });

  console.log('DEBUG: Context menu created');

  // Initialize settings using centralized configuration
  // Note: Since this is a service worker, we need to import config values
  // We'll use the same default settings as defined in config.js
  chrome.storage.local.get('settings', (data) => {
    if (!data.settings) {
      // Use the same default settings as in config.js
      chrome.storage.local.set({
        settings: {
          environment: 'local', // matches config.js DEFAULT_SETTINGS
          saveHistory: true,
          useMockApi: false,
          hideFloatingIconGlobally: false,
          hiddenDomains: []
        }
      });
    } else {
      // Ensure new settings exist in existing settings
      const settings = data.settings;
      let needsUpdate = false;

      if (settings.hideFloatingIconGlobally === undefined) {
        settings.hideFloatingIconGlobally = false;
        needsUpdate = true;
      }

      if (!settings.hiddenDomains) {
        settings.hiddenDomains = [];
        needsUpdate = true;
      }

      // Allow local environment for development
      // Note: Removed forced migration to allow local development

      if (needsUpdate) {
        chrome.storage.local.set({ settings: settings });
      }
    }
  });

  // Debug: Check initial user state
  console.log('DEBUG: Checking initial user state...');
  getUserData().then(userData => {
    console.log('DEBUG: Initial user state:', userData ? 'User logged in' : 'No user logged in');
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'replypal') {
    // Store the selected text if available
    if (info.selectionText) {
      chrome.storage.local.set({ selectedText: info.selectionText });
    } else {
      // Clear any previously selected text if no text is selected
      chrome.storage.local.remove('selectedText');
    }

    // Function to send message to content script
    const sendMessageToContentScript = () => {
      chrome.tabs.sendMessage(tab.id, {
        action: 'showPopup',
        text: info.selectionText || ''
      }).catch(err => {
        console.log('Error sending message to content script:', err);
      });
    };

    // Check if the tab is already ready
    if (readyTabs.has(tab.id)) {
      console.log(`Tab ${tab.id} already has content script ready`);
      sendMessageToContentScript();
    } else {
      console.log(`Injecting content script into tab ${tab.id}`);
      // Inject the content script
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        files: ['js/content.js']
      }, () => {
        // Wait for the content script to initialize
        const maxWaitTime = 1000; // 1 second
        const startTime = Date.now();

        const checkReady = () => {
          if (readyTabs.has(tab.id)) {
            console.log(`Tab ${tab.id} is now ready, sending message`);
            sendMessageToContentScript();
          } else if (Date.now() - startTime < maxWaitTime) {
            // Still within wait time, check again in 100ms
            setTimeout(checkReady, 100);
          } else {
            // Timeout reached, try sending message anyway
            console.log(`Timeout reached for tab ${tab.id}, sending message anyway`);
            sendMessageToContentScript();
          }
        };

        checkReady();
      });
    }
  }
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // Track when content scripts are ready
  if (message.action === 'contentScriptReady' && sender.tab) {
    console.log(`Content script ready in tab ${sender.tab.id}`);
    readyTabs.add(sender.tab.id);
    sendResponse({ success: true });
  }
  else if (message.action === 'storeSelectedText') {
    // Store the selected text
    chrome.storage.local.set({ selectedText: message.text });
    sendResponse({ success: true });
  }
  else if (message.action === 'generateFromContent') {
    // Forward to API
    handleGenerateRequest(message.data)
      .then(response => {
        sendResponse({ success: true, data: response });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // Indicate async response
  }
  else if (message.action === 'generateStreamingResponse') {
    // Handle streaming response
    handleStreamingRequest(message.data, sender.tab.id)
      .then(() => {
        sendResponse({ success: true });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // Indicate async response
  }
  else if (message.action === 'copyToClipboard') {
    // Copy text to clipboard
    copyToClipboard(message.text)
      .then(() => {
        sendResponse({ success: true });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // Indicate async response
  }
  else if (message.action === 'authenticateWithGoogle') {
    // Handle Google authentication
    console.log('=== DEBUG: authenticateWithGoogle started ===');
    chrome.identity.getAuthToken({ interactive: true }, (token) => {
      if (chrome.runtime.lastError) {
        console.error('DEBUG: Chrome identity error:', chrome.runtime.lastError);
        sendResponse({ success: false, error: chrome.runtime.lastError.message });
      } else {
        console.log('DEBUG: Received Google auth token:', token ? 'Token received' : 'No token');
        if (token) {
          console.log('DEBUG: Google token (first 20 chars):', token.substring(0, 20) + '...');
        }

        // Get user info using the token
        console.log('DEBUG: Fetching user info from Google API');
        fetch('https://www.googleapis.com/oauth2/v1/userinfo?alt=json', {
          headers: { Authorization: `Bearer ${token}` }
        })
          .then(response => {
            console.log('DEBUG: Google userinfo response status:', response.status);
            return response.json();
          })
          .then(userInfo => {
            console.log('DEBUG: Received user info from Google:', userInfo);
            // Get API token from backend
            console.log('DEBUG: Requesting API token from backend');
            getApiToken({
              uid: userInfo.id,
              email: userInfo.email,
              name: userInfo.name,
              picture: userInfo.picture
            })
              .then(tokenResponse => {
                // Store user info and API token
                console.log('DEBUG: Received API token from backend:', tokenResponse);
                const userToStore = {
                  uid: userInfo.id,
                  displayName: userInfo.name,
                  email: userInfo.email,
                  photoURL: userInfo.picture,
                  apiToken: tokenResponse.access_token
                };
                console.log('DEBUG: Storing user data with token');
                console.log('DEBUG: User to store:', {
                  ...userToStore,
                  apiToken: userToStore.apiToken ? userToStore.apiToken.substring(0, 20) + '...' : 'No token'
                });
                chrome.storage.local.set({
                  user: userToStore
                }, () => {
                  if (chrome.runtime.lastError) {
                    console.error('DEBUG: Error storing user data:', chrome.runtime.lastError);
                  } else {
                    console.log('DEBUG: User data with token stored successfully');
                  }
                });
                sendResponse({ success: true, user: userInfo });
              })
              .catch(error => {
                console.error('DEBUG: Error getting API token:', error);
                console.error('DEBUG: Storing user info without API token');
                // Still store user info without API token
                chrome.storage.local.set({
                  user: {
                    uid: userInfo.id,
                    displayName: userInfo.name,
                    email: userInfo.email,
                    photoURL: userInfo.picture
                  }
                });
                sendResponse({ success: true, user: userInfo });
              });
          })
          .catch(error => {
            console.error('DEBUG: Error fetching user info from Google:', error);
            sendResponse({ success: false, error: error.message });
          });
      }
    });
    return true; // Indicate async response
  }
  else if (message.action === 'signOut') {
    // Handle sign out
    chrome.identity.clearAllCachedAuthTokens(() => {
      chrome.storage.local.remove('user');
      sendResponse({ success: true });
    });
    return true; // Indicate async response
  }

  return true;
});

// Handle tab updates
chrome.tabs.onUpdated.addListener((tabId, changeInfo) => {
  if (changeInfo.status === 'loading') {
    readyTabs.delete(tabId);
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // Clear any previously selected text
  chrome.storage.local.remove('selectedText');

  // Function to send message to content script
  const sendMessageToContentScript = () => {
    chrome.tabs.sendMessage(tab.id, {
      action: 'showPopup'
    }).catch(err => {
      console.log('Error sending message to content script:', err);
    });
  };

  // Check if the tab is already ready
  if (readyTabs.has(tab.id)) {
    console.log(`Tab ${tab.id} already has content script ready`);
    sendMessageToContentScript();
  } else {
    console.log(`Injecting content script into tab ${tab.id}`);
    // Inject the content script
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['js/content.js']
    }, () => {
      // Wait for the content script to initialize
      const maxWaitTime = 1000; // 1 second
      const startTime = Date.now();

      const checkReady = () => {
        if (readyTabs.has(tab.id)) {
          console.log(`Tab ${tab.id} is now ready, sending message`);
          sendMessageToContentScript();
        } else if (Date.now() - startTime < maxWaitTime) {
          // Still within wait time, check again in 100ms
          setTimeout(checkReady, 100);
        } else {
          // Timeout reached, try sending message anyway
          console.log(`Timeout reached for tab ${tab.id}, sending message anyway`);
          sendMessageToContentScript();
        }
      };

      checkReady();
    });
  }
});

/**
 * Handle generate request
 * @param {Object} requestData - Request data
 * @returns {Promise<Object>} Response data
 */
async function handleGenerateRequest(requestData) {
  try {
    console.log('=== DEBUG: handleGenerateRequest started ===');
    console.log('Request data:', requestData);

    // Get settings
    const settings = await getSettings();
    console.log('DEBUG: Settings retrieved:', settings);

    // Check if mock API is enabled
    if (settings.useMockApi) {
      console.log('DEBUG: Using mock API');
      return mockGenerateResponse(requestData);
    }

    // Get API URL using centralized configuration
    const environment = settings.environment || getDefaultEnvironment();
    const apiUrl = getApiUrlForEnvironment(environment);
    console.log('DEBUG: Environment:', environment);
    console.log('DEBUG: API URL:', apiUrl);

    // Get authentication token
    const userData = await getUserData();
    const authToken = userData?.apiToken;
    console.log('DEBUG: User data exists:', !!userData);
    console.log('DEBUG: User data details:', userData);
    console.log('DEBUG: Auth token exists:', !!authToken);
    if (authToken) {
      console.log('DEBUG: Auth token (first 20 chars):', authToken.substring(0, 20) + '...');
    }

    // Prepare headers
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    // Add authorization header if token exists
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
      console.log('DEBUG: Authorization header added');
    } else {
      console.warn('DEBUG: No auth token available - request will be unauthorized');
    }

    console.log('DEBUG: Request headers:', headers);

    // Make API request
    console.log('DEBUG: Making API request to:', `${apiUrl}/generate`);
    const response = await fetch(`${apiUrl}/generate`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });

    console.log('Background script - API request URL:', `${apiUrl}/generate`);
    console.log('Background script - API response status:', response.status);
    console.log('Background script - API response text:', response.statusText);

    if (!response.ok) {
      console.error('DEBUG: API request failed');
      console.error('DEBUG: Response status:', response.status);
      console.error('DEBUG: Response status text:', response.statusText);

      // Try to get response body for more details
      try {
        const errorBody = await response.text();
        console.error('DEBUG: Error response body:', errorBody);
      } catch (e) {
        console.error('DEBUG: Could not read error response body:', e);
      }

      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    console.log('DEBUG: API request successful');
    const responseData = await response.json();
    console.log('DEBUG: Response data received');
    return responseData;
  } catch (error) {
    console.error('DEBUG: Error in handleGenerateRequest:', error);
    console.error('DEBUG: Error stack:', error.stack);
    throw error;
  }
}

/**
 * Handle streaming request
 * @param {Object} requestData - Request data
 * @param {number} tabId - Tab ID
 * @returns {Promise<void>}
 */
async function handleStreamingRequest(requestData, tabId) {
  try {
    console.log('=== DEBUG: handleStreamingRequest started ===');
    console.log('Request data:', requestData);
    console.log('Tab ID:', tabId);

    // Get settings
    const settings = await getSettings();
    console.log('DEBUG: Settings retrieved:', settings);

    // Check if mock API is enabled
    if (settings.useMockApi) {
      console.log('DEBUG: Using mock streaming API');
      return mockStreamingResponse(requestData, tabId);
    }

    // Get API URL using centralized configuration
    const environment = settings.environment || getDefaultEnvironment();
    const apiUrl = getApiUrlForEnvironment(environment);
    console.log('DEBUG: Environment:', environment);
    console.log('DEBUG: API URL:', apiUrl);

    // Get authentication token
    const userData = await getUserData();
    const authToken = userData?.apiToken;

    console.log('Background script - User data for API request:', userData ? 'Found' : 'Not found');
    console.log('Background script - Auth token for API request:', authToken ? 'Found' : 'Not found');
    console.log('DEBUG: User data details:', userData);
    if (authToken) {
      console.log('DEBUG: Auth token (first 20 chars):', authToken.substring(0, 20) + '...');
    }

    // Prepare headers
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream'
    };

    // Add authorization header if token exists
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
      console.log('Background script - Added Authorization header to request');
    } else {
      console.warn('Background script - No auth token available for API request');
    }

    console.log('DEBUG: Request headers:', headers);

    // Make API request
    console.log('DEBUG: Making streaming API request to:', `${apiUrl}/generate_stream`);
    const response = await fetch(`${apiUrl}/generate_stream`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });

    console.log('DEBUG: Streaming API response status:', response.status);
    console.log('DEBUG: Streaming API response status text:', response.statusText);

    if (!response.ok) {
      console.error('DEBUG: Streaming API request failed');
      console.error('DEBUG: Response status:', response.status);
      console.error('DEBUG: Response status text:', response.statusText);

      // Try to get response body for more details
      try {
        const errorBody = await response.text();
        console.error('DEBUG: Error response body:', errorBody);
      } catch (e) {
        console.error('DEBUG: Could not read error response body:', e);
      }

      // For 429 errors, try to get the detailed error message from the response
      if (response.status === 429) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API error: ${response.status} ${errorData.detail || response.statusText}`);
      } else {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
    }

    // Process the stream
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.substring(6);

          // Send chunk to content script
          chrome.tabs.sendMessage(tabId, {
            action: 'streamResponseChunk',
            chunk: data
          });

          if (data === '[DONE]') return;
        }
      }
    }

    // Send done signal if not already sent
    chrome.tabs.sendMessage(tabId, {
      action: 'streamResponseChunk',
      chunk: '[DONE]'
    });
  } catch (error) {
    // Don't log to console, just send the error to the content script

    // Send error to content script
    chrome.tabs.sendMessage(tabId, {
      action: 'streamResponseError',
      error: error.message
    });

    // Don't throw the error, just return to avoid console errors
    return;
  }
}

/**
 * Copy text to clipboard
 * @param {string} text - Text to copy
 * @returns {Promise<void>}
 */
async function copyToClipboard(text) {
  try {
    // Get the current active tab
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tabs || tabs.length === 0) {
      throw new Error('No active tab found');
    }

    // Execute script in the active tab to copy text
    await chrome.scripting.executeScript({
      target: { tabId: tabs[0].id },
      func: textToCopy => {
        // This function runs in the context of the page
        const textarea = document.createElement('textarea');
        textarea.value = textToCopy;
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
      },
      args: [text]
    });

    return Promise.resolve();
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    return Promise.reject(new Error('Failed to copy text to clipboard: ' + error.message));
  }
}

/**
 * Get default environment (matches config.js DEFAULT_SETTINGS)
 * @returns {string} The default environment
 */
function getDefaultEnvironment() {
  return 'local'; // matches config.js DEFAULT_SETTINGS.environment
}

/**
 * Get API URLs object (matches config.js API_URLS)
 * @returns {Object} API URLs for all environments
 */
function getApiUrls() {
  // These values must match config.js API_URLS exactly
  return {
    local: 'http://localhost:8000',
    dev: 'https://dev-api.replypal.com',
    prod: 'https://n9dk65q3fd.execute-api.us-east-1.amazonaws.com/production'
  };
}

/**
 * Get API URL for environment (uses centralized configuration values)
 * @param {string} environment - The environment (local, dev, prod)
 * @returns {string} The API URL for the specified environment
 */
function getApiUrlForEnvironment(environment) {
  const apiUrls = getApiUrls();
  return apiUrls[environment] || apiUrls[getDefaultEnvironment()];
}

/**
 * Get default settings (matches config.js DEFAULT_SETTINGS)
 * @returns {Object} Default settings object
 */
function getDefaultSettings() {
  // These values must match config.js DEFAULT_SETTINGS exactly
  return {
    environment: 'local',
    saveHistory: true,
    useMockApi: false,
    hideFloatingIconGlobally: false,
    hiddenDomains: []
  };
}

/**
 * Get settings from storage
 * @returns {Promise<Object>} Settings object
 */
function getSettings() {
  return new Promise((resolve) => {
    chrome.storage.local.get('settings', (data) => {
      resolve(data.settings || getDefaultSettings());
    });
  });
}

/**
 * Get user data from storage
 * @returns {Promise<Object|null>} User data object or null if not logged in
 */
function getUserData() {
  return new Promise((resolve) => {
    console.log('DEBUG: getUserData called');
    chrome.storage.local.get('user', (data) => {
      if (chrome.runtime.lastError) {
        console.error('DEBUG: Error retrieving user data:', chrome.runtime.lastError);
        resolve(null);
        return;
      }

      const userData = data.user || null;
      console.log('DEBUG: Retrieved user data from storage:', userData ? 'Found' : 'Not found');
      if (userData) {
        console.log('DEBUG: User ID:', userData.uid);
        console.log('DEBUG: User email:', userData.email);
        console.log('DEBUG: User display name:', userData.displayName);
        console.log('DEBUG: API Token exists:', !!userData.apiToken);
        if (userData.apiToken) {
          console.log('DEBUG: API Token (first 20 chars):', userData.apiToken.substring(0, 20) + '...');
        }
      } else {
        console.log('DEBUG: No user data found in storage');
      }

      resolve(userData);
    });
  });
}

/**
 * Mock generate response for testing
 * @param {Object} requestData - Request data
 * @returns {Promise<Object>} Mock response
 */
function mockGenerateResponse(requestData) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        response: `This is a mock response for: "${requestData.user_intent}"\n\nSelected text: "${requestData.selected_text}"\n\nTone: ${requestData.tone}\nPurpose: ${requestData.purpose}`
      });
    }, 1000);
  });
}

/**
 * Get API token from backend
 * @param {Object} userData - User data from Google authentication
 * @returns {Promise<Object>} Token response
 */
async function getApiToken(userData) {
  try {
    console.log('=== DEBUG: getApiToken started ===');
    console.log('DEBUG: User data for token request:', userData);

    // Get settings
    const settings = await getSettings();
    console.log('DEBUG: Settings for token request:', settings);

    // Get API URL using centralized configuration
    const environment = settings.environment || getDefaultEnvironment();
    const apiUrl = getApiUrlForEnvironment(environment);
    console.log('DEBUG: Environment for token request:', environment);
    console.log('DEBUG: API URL for token request:', apiUrl);

    const requestBody = {
      uid: userData.uid,
      email: userData.email,
      name: userData.name,
      picture: userData.picture
    };
    console.log('DEBUG: Token request body:', requestBody);

    // Make API request to get token
    console.log('DEBUG: Making token request to:', `${apiUrl}/auth/token`);
    const response = await fetch(`${apiUrl}/auth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('DEBUG: Token response status:', response.status);
    console.log('DEBUG: Token response status text:', response.statusText);

    if (!response.ok) {
      console.error('DEBUG: Token request failed');
      console.error('DEBUG: Response status:', response.status);
      console.error('DEBUG: Response status text:', response.statusText);

      // Try to get response body for more details
      try {
        const errorBody = await response.text();
        console.error('DEBUG: Token error response body:', errorBody);
      } catch (e) {
        console.error('DEBUG: Could not read token error response body:', e);
      }

      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const tokenResponse = await response.json();
    console.log('DEBUG: Token response received:', tokenResponse);
    return tokenResponse;
  } catch (error) {
    console.error('DEBUG: Error getting API token:', error);
    console.error('DEBUG: Error stack:', error.stack);
    throw error;
  }
}

/**
 * Mock streaming response for testing
 * @param {Object} requestData - Request data
 * @param {number} tabId - Tab ID
 * @returns {Promise<void>}
 */
function mockStreamingResponse(requestData, tabId) {
  return new Promise((resolve) => {
    const mockResponse = `This is a mock streaming response for: "${requestData.user_intent}"\n\nSelected text: "${requestData.selected_text}"\n\nTone: ${requestData.tone}\nPurpose: ${requestData.purpose}`;

    // Split the response into chunks
    const chunks = mockResponse.split(' ');

    // Send chunks with delay
    let i = 0;
    const interval = setInterval(() => {
      if (i < chunks.length) {
        chrome.tabs.sendMessage(tabId, {
          action: 'streamResponseChunk',
          chunk: chunks[i] + ' '
        });
        i++;
      } else {
        clearInterval(interval);

        // Send done signal
        chrome.tabs.sendMessage(tabId, {
          action: 'streamResponseChunk',
          chunk: '[DONE]'
        });

        resolve();
      }
    }, 100);
  });
}
